{"name": "@ghq-abi/northstar-domain", "version": "0.0.7", "description": "northstar domain package, here we have types, entities and repos", "main": "./dist/index.js", "types": "./dist/index.d.ts", "license": "ISC", "engine": {"node": ">=22.16.0", "npm": ">=10.9.2", "yarn": ">=1.22.0"}, "scripts": {"build": "rm -rf ./dist && tsc", "lint": "eslint ./src/**/*.ts --fix", "test:cov": "jest --coverage", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm migration:generate -- -d ./src/configs/typeorm.datasource.ts ./src/database/migrations/$npm_config_name", "migration:run": "npm run typeorm migration:run -- -d ./src/configs/typeorm.datasource.ts", "migration:revert": "npm run typeorm migration:revert -- -d ./src/configs/typeorm.datasource.ts", "migration:show": "npm run typeorm migration:show -- -d ./src/configs/typeorm.datasource.ts", "seed": "ts-node -r tsconfig-paths/register src/database/seeds/index.ts"}, "repository": {"type": "git", "url": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/"}, "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/typeorm": "^11.0.0", "dotenv": "^17.2.1", "mssql": "^11.0.1", "typeorm": "^0.3.25"}, "devDependencies": {"@types/jest": "^29.5.2", "@types/mssql": "^9.1.7", "@types/node": "^20.12.5", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.6.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.3"}}