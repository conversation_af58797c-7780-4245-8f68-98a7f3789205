import { dataSource } from '../../configs/typeorm.datasource';
import { runDeliverablesTypesSeed } from './deliverables-types.seed';

async function runSeed() {
  await dataSource.initialize();
  console.log('📦 Database Connected.');
  await runDeliverablesTypesSeed(dataSource);
  console.log('✅ Seed completed!');
  await dataSource.destroy();
}

runSeed().catch((err) => {
  console.error('❌', err);
  process.exit(1);
});
