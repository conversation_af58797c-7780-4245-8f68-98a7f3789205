import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBusinessFunctions1755559677897 implements MigrationInterface {
    name = 'AddBusinessFunctions1755559677897'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "ix_business_functions_str_lang_code" ON "tsc-dev"."business_functions"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP CONSTRAINT "PK__business__AE0675968BBB1255"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD CONSTRAINT "PK__business__AE0675968BBB1255" PRIMARY KEY ("str_lang_code")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "uid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP CONSTRAINT "PK__business__AE0675968BBB1255"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "str_lang_code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "str_name"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "uid_employee_updated"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "uid_employee_deleted"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "startTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "endTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "str_business_function_code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "code" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD CONSTRAINT "PK_e657a74b6814355e3185975b336" PRIMARY KEY ("code")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "label" nvarchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "label"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP CONSTRAINT "PK_e657a74b6814355e3185975b336"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "str_business_function_code" nvarchar(300)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "endTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "startTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "uid_employee_deleted" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "uid_employee_updated" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "str_name" nvarchar(300) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "str_lang_code" char(5) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD CONSTRAINT "PK__business__AE0675968BBB1255" PRIMARY KEY ("str_lang_code")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD "uid" uniqueidentifier NOT NULL CONSTRAINT "DF_280a3b72dbb65f57e3772db7000" DEFAULT NEWSEQUENTIALID()`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" DROP CONSTRAINT "PK__business__AE0675968BBB1255"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."business_functions" ADD CONSTRAINT "PK__business__AE0675968BBB1255" PRIMARY KEY ("uid", "str_lang_code")`);
        await queryRunner.query(`CREATE INDEX "ix_business_functions_str_lang_code" ON "tsc-dev"."business_functions" ("str_lang_code") `);
    }

}
