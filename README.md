# NorthStar Domain

## Entities

Here we define the entities to reuse them on all APIs.

## Repositories

Here we save typeorm repositories to reuse them on all APIs.

## Installation

Run `yarn` or `npm install` using node >=22.

## Getting started
## Database Migrations & Seeds

### Migrations

**Generate a migration:**

```bash
npm run migration:generate --name=MigrationName
```
Replace `MigrationName` with your desired migration name. The migration will be created in `src/migrations/`.

**Run migrations:**

```bash
npm run migration:run
```

**Revert the last migration:**

```bash
npm run migration:revert
```

**Show migration status:**

```bash
npm run migration:show
```

### Seeds

**Run the seed script:**

```bash
npm run seed
```
This will execute `src/database/seeds.ts` and populate the database with mock data.

Import the repositories inside your module and add them on providers and the related entity on your TypeormModule config.

```Typescript
// ...

import { MyEntity, MyRepository } from '@ghq-abi/northstar-domain';

// ...
  imports: [
    // ...
    TypeOrmModule.forFeature([
      // ...
      MyEntity,
      // ...
    ]),
    // ...
  ],
  providers: [
    // ...
    MyRepository,
    // ...
  ]

// ...
```

To be able to export and use on the services/use-cases.

```Typescript
// ...

import { MyEntity, MyRepository } from '@ghq-abi/northstar-domain';

// ...

  constructor(
    // ...
    private readonly myRepository: MyRepository,
    // ...
  ) { }

// ...

  saveMy(my: MyEntity): MyEntity {
    return this.myRepository.save(myr);
  }

// ...
```

## Database Structure (Mermaid ER Diagram)

All ERDs are written using Mermaid syntax, which allows visualizing database models directly in Markdown-compatible tools, and can be found near each respective entity. A complete ERD can be found as index.mmd on entities root folder.

|
|_entities
|__example.entity.ts
|__example.mmd
|
|__index.mmd
